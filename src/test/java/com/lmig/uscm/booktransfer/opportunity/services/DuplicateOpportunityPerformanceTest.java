package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJDBCRepo;
import com.lmig.uscm.booktransfer.opportunity.services.testutils.OpportunityTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Performance tests for the DuplicateOpportunityService to ensure it can handle
 * bulk operations efficiently when withdrawing thousands of opportunities.
 */
@ExtendWith(MockitoExtension.class)
class DuplicateOpportunityPerformanceTest {

    @Mock
    private OpportunityJDBCRepo opportunityJDBCRepo;

    private DuplicateOpportunityService duplicateOpportunityService;

    @BeforeEach
    void setUp() {
        duplicateOpportunityService = new DuplicateOpportunityService(opportunityJDBCRepo);
    }

    @Test
    void testBulkWithdrawal_1000Opportunities_CompletesWithinTimeLimit() {
        performBulkWithdrawalTest(1000, 10000);
    }

    @Test
    void testBulkWithdrawal_5000Opportunities_CompletesWithinTimeLimit() {
        performBulkWithdrawalTest(5000, 30000);
    }

    private void performBulkWithdrawalTest(int opportunityCount, long maxDurationMs) {
        // Given: opportunities to be withdrawn
        List<Opportunity> opportunities = OpportunityTestUtils.createBulkOpportunities(opportunityCount);

        // Mock repository to return some duplicates for each opportunity
        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria(anyString(), anyString(), anyInt()))
                .thenReturn(OpportunityTestUtils.createMockDuplicates());

        // When: Process all opportunities and measure time
        long startTime = System.nanoTime();

        for (Opportunity opp : opportunities) {
            duplicateOpportunityService.getOpportunityForCustomerPolicyUpdate(opp);
        }

        long endTime = System.nanoTime();
        long durationMs = TimeUnit.NANOSECONDS.toMillis(endTime - startTime);

        // Then: Should complete within reasonable time
        assertTrue(durationMs < maxDurationMs,
                String.format("Bulk processing took %d ms, expected < %d ms", durationMs, maxDurationMs));

        System.out.println(String.format("Processed %d opportunities in %d ms (avg: %.2f ms per opportunity)",
                opportunityCount, durationMs, (double) durationMs / opportunityCount));
    }

    @Test
    void testMemoryUsage_BulkProcessing_DoesNotCauseMemoryLeak() {
        // Given: Large number of opportunities
        List<Opportunity> opportunities = OpportunityTestUtils.createBulkOpportunities(2000);

        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria(anyString(), anyString(), anyInt()))
                .thenReturn(OpportunityTestUtils.createMockDuplicates());

        // When: Process opportunities and measure memory
        Runtime runtime = Runtime.getRuntime();
        runtime.gc(); // Force garbage collection before measurement
        long memoryBefore = runtime.totalMemory() - runtime.freeMemory();

        for (Opportunity opp : opportunities) {
            duplicateOpportunityService.findLatestActiveDuplicateOpportunity(opp);
        }

        runtime.gc(); // Force garbage collection after processing
        long memoryAfter = runtime.totalMemory() - runtime.freeMemory();
        long memoryUsed = memoryAfter - memoryBefore;

        // Then: Memory usage should be reasonable (less than 100MB for 2000 operations)
        assertTrue(memoryUsed < 100 * 1024 * 1024,
                String.format("Memory usage was %d bytes, expected < 100MB", memoryUsed));

        System.out.println(String.format("Memory used for 2000 operations: %.2f MB",
                (double) memoryUsed / (1024 * 1024)));
    }

    @Test
    void testConcurrentAccess_MultipleThreads_HandlesCorrectly() throws InterruptedException {
        // Given: Multiple threads processing opportunities concurrently
        List<Opportunity> opportunities = OpportunityTestUtils.createBulkOpportunities(500);

        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria(anyString(), anyString(), anyInt()))
                .thenReturn(OpportunityTestUtils.createMockDuplicates());

        int numThreads = 5;
        List<Thread> threads = new ArrayList<>();
        List<Exception> exceptions = new ArrayList<>();

        // When: Process opportunities concurrently
        for (int i = 0; i < numThreads; i++) {
            final int threadIndex = i;
            Thread thread = new Thread(() -> {
                try {
                    int startIndex = threadIndex * 100;
                    int endIndex = Math.min(startIndex + 100, opportunities.size());
                    
                    for (int j = startIndex; j < endIndex; j++) {
                        duplicateOpportunityService.getOpportunityForCustomerPolicyUpdate(opportunities.get(j));
                    }
                } catch (Exception e) {
                    synchronized (exceptions) {
                        exceptions.add(e);
                    }
                }
            });
            threads.add(thread);
            thread.start();
        }

        // Wait for all threads to complete
        for (Thread thread : threads) {
            thread.join(10000); // 10 second timeout
        }

        // Then: No exceptions should occur
        assertTrue(exceptions.isEmpty(), 
                String.format("Concurrent processing failed with %d exceptions: %s", 
                        exceptions.size(), exceptions.toString()));
    }

    @Test
    void testDatabaseQueryOptimization_BulkOperations_MinimizesQueries() {
        // Given: Opportunities with same customer criteria (should reuse results)
        List<Opportunity> opportunities = OpportunityTestUtils.createOpportunitiesWithSameCriteria(100);

        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria("POLICY123", "HOME", 100))
                .thenReturn(OpportunityTestUtils.createMockDuplicates());

        // When: Process all opportunities
        long startTime = System.nanoTime();

        for (Opportunity opp : opportunities) {
            duplicateOpportunityService.findLatestActiveDuplicateOpportunity(opp);
        }

        long endTime = System.nanoTime();
        long durationMs = TimeUnit.NANOSECONDS.toMillis(endTime - startTime);

        // Then: Should complete quickly since all opportunities have same criteria
        assertTrue(durationMs < 1000,
                String.format("Processing 100 opportunities with same criteria took %d ms, expected < 1000 ms", durationMs));

        System.out.println(String.format("Processed 100 opportunities with same criteria in %d ms", durationMs));
    }


}
