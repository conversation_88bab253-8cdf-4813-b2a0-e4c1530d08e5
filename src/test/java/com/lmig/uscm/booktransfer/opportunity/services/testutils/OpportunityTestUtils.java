package com.lmig.uscm.booktransfer.opportunity.services.testutils;

import com.lmig.uscm.booktransfer.opportunity.client.domain.LineType;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Utility class for creating test opportunities to reduce code duplication across test files.
 */
public class OpportunityTestUtils {

    /**
     * Creates a basic opportunity with the specified parameters.
     */
    public static Opportunity createOpportunity(int id, String priorCarrierGuid, String businessType, 
                                              int bookTransferID, OpportunityStatus status) {
        Opportunity opp = new Opportunity();
        opp.setOpportunityId(id);
        opp.setPriorCarrierGuid(priorCarrierGuid);
        opp.setBusinessType(businessType);
        opp.setBookTransferID(bookTransferID);
        opp.setStatus(status.getOppStatusCode());
        opp.setData("<xml>test</xml>");
        opp.setOriginalXML("<xml>original</xml>");
        return opp;
    }

    /**
     * Creates a business opportunity with LineType.Business set.
     */
    public static Opportunity createBusinessOpportunity(int id, String priorCarrierGuid, String businessType, 
                                                       int bookTransferID, OpportunityStatus status) {
        Opportunity opp = createOpportunity(id, priorCarrierGuid, businessType, bookTransferID, status);
        opp.setLineType(LineType.Business);
        opp.setData("<xml><test>data</test></xml>");
        opp.setOriginalXML("<xml><original>data</original></xml>");
        return opp;
    }

    /**
     * Creates a list of opportunities with the same criteria for testing duplicate scenarios.
     */
    public static List<Opportunity> createOpportunitiesWithSameCriteria(int count) {
        List<Opportunity> opportunities = new ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            Opportunity opp = new Opportunity();
            opp.setOpportunityId(i);
            opp.setPriorCarrierGuid("POLICY123"); // Same criteria
            opp.setBusinessType("HOME"); // Same criteria
            opp.setBookTransferID(100); // Same criteria
            opp.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode());
            opp.setLineType(LineType.Business);
            opp.setData("<xml><test>data" + i + "</test></xml>");
            opp.setOriginalXML("<xml><original>data" + i + "</original></xml>");
            opportunities.add(opp);
        }
        
        return opportunities;
    }

    /**
     * Creates a list of bulk opportunities with varied criteria for performance testing.
     */
    public static List<Opportunity> createBulkOpportunities(int count) {
        List<Opportunity> opportunities = new ArrayList<>();
        
        for (int i = 1; i <= count; i++) {
            Opportunity opp = new Opportunity();
            opp.setOpportunityId(i);
            opp.setPriorCarrierGuid("POLICY" + (i % 100)); // Create some variety
            opp.setBusinessType(i % 2 == 0 ? "HOME" : "AUTO"); // Alternate between HOME and AUTO
            opp.setBookTransferID(100 + (i % 10)); // Create some variety in book IDs
            opp.setStatus(OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN.getOppStatusCode());
            opp.setLineType(LineType.Business);
            opp.setData("<xml><test>data" + i + "</test></xml>");
            opp.setOriginalXML("<xml><original>data" + i + "</original></xml>");
            opportunities.add(opp);
        }
        
        return opportunities;
    }

    /**
     * Creates a standard set of test opportunities for common test scenarios.
     */
    public static TestOpportunitySet createStandardTestSet() {
        Opportunity currentOpp = createOpportunity(1, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN);
        Opportunity duplicate1 = createOpportunity(2, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED);
        Opportunity duplicate2 = createOpportunity(3, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS);
        
        List<Opportunity> allMatches = Arrays.asList(currentOpp, duplicate1, duplicate2);
        
        return new TestOpportunitySet(currentOpp, duplicate1, duplicate2, allMatches);
    }

    /**
     * Creates a test set with withdrawn opportunities for testing scenarios with no active duplicates.
     */
    public static TestOpportunitySet createWithdrawnTestSet() {
        Opportunity currentOpp = createOpportunity(1, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN);
        Opportunity duplicate1 = createOpportunity(2, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN);
        Opportunity duplicate2 = createOpportunity(3, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_ISSUED);
        
        List<Opportunity> allMatches = Arrays.asList(currentOpp, duplicate1, duplicate2);
        
        return new TestOpportunitySet(currentOpp, duplicate1, duplicate2, allMatches);
    }

    /**
     * Creates mock duplicates for performance testing.
     */
    public static List<Opportunity> createMockDuplicates() {
        List<Opportunity> duplicates = new ArrayList<>();
        
        // Add a few mock duplicates
        duplicates.add(createOpportunity(100, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED));
        duplicates.add(createOpportunity(101, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS));
        duplicates.add(createOpportunity(102, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN));
        
        return duplicates;
    }

    /**
     * Data class to hold a set of test opportunities for common test scenarios.
     */
    public static class TestOpportunitySet {
        public final Opportunity currentOpp;
        public final Opportunity duplicate1;
        public final Opportunity duplicate2;
        public final List<Opportunity> allMatches;

        public TestOpportunitySet(Opportunity currentOpp, Opportunity duplicate1, Opportunity duplicate2, List<Opportunity> allMatches) {
            this.currentOpp = currentOpp;
            this.duplicate1 = duplicate1;
            this.duplicate2 = duplicate2;
            this.allMatches = allMatches;
        }
    }
}
