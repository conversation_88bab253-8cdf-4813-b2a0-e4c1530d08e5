package com.lmig.uscm.booktransfer.opportunity.repo;

import com.lmig.uscm.booktransfer.opportunity.OpportunityApplication;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.mockito.domain.BookTransfer;
import com.lmig.uscm.booktransfer.opportunity.mockito.repos.BookTransferRepository;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJpaRepository;
import com.lmig.uscm.booktransfer.opportunity.services.testutils.OpportunityTestUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * Database integration test for duplicate opportunity detection functionality
 */
@SpringBootTest(classes = OpportunityApplication.class)
@ExtendWith(SpringExtension.class)
@TestPropertySource(properties = {
        "spring.profiles.active=unit,testing,transformationClient,mongoTestContainer"
})
@EntityScan({"com.lmig.uscm.booktransfer.opportunity.mockito.domain", "com.lmig.uscm.booktransfer.opportunity.domain"})
@EnableJpaRepositories(basePackages = {"com.lmig.uscm.booktransfer.opportunity.mockito.repos", "com.lmig.uscm.booktransfer.opportunity.repo"})
class DuplicateOpportunityDatabaseTest {

    @Autowired
    private OpportunityJDBCRepo opportunityJDBCRepo;

    @Autowired
    private BookTransferRepository bookTransferRepository;

    @Autowired
    private OpportunityJpaRepository opportunityRepository;

    @BeforeEach
    void setUp() {
        bookTransferRepository.deleteAll();
        opportunityRepository.deleteAll();
        setupTestData();
    }

    @Test
    void testFindOpportunitiesByCustomerCriteria_WithDuplicates() {
        // Test the actual SQL query against H2 database
        List<Opportunity> duplicates = opportunityJDBCRepo.findOpportunitiesByCustomerCriteria(
                "POLICY123", "HOME", 100);
        
        assertEquals(3, duplicates.size());
        
        // Verify all have same customer criteria
        duplicates.forEach(opp -> {
            assertEquals("POLICY123", opp.getPriorCarrierGuid());
            assertEquals("HOME", opp.getBusinessType());
            assertEquals(100, opp.getBookTransferID());
        });
    }

    @Test
    void testFindOpportunitiesByCustomerCriteria_NoMatches() {
        List<Opportunity> duplicates = opportunityJDBCRepo.findOpportunitiesByCustomerCriteria(
                "NONEXISTENT", "HOME", 100);
        
        assertTrue(duplicates.isEmpty());
    }

    @Test
    void testFindOpportunitiesByCustomerCriteria_PartialMatch() {
        // Should not return results for partial matches - different book ID
        List<Opportunity> duplicates = opportunityJDBCRepo.findOpportunitiesByCustomerCriteria(
                "POLICY123", "HOME", 200); // Different book ID

        assertTrue(duplicates.isEmpty());
    }

    private void setupTestData() {
        // Create book transfer
        BookTransfer bookTransfer = new BookTransfer();
        bookTransfer.setBookTransferID(100);
        bookTransfer.setAgentNum("AGENT123");
        bookTransfer.setStatus("active");
        bookTransferRepository.saveAndFlush(bookTransfer);

        // Create duplicate opportunities with same customer criteria
        createOpportunity(1, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS);
        createOpportunity(2, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN);
        createOpportunity(3, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_ISSUED);
        
        // Create non-matching opportunities
        createOpportunity(4, "POLICY456", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS);
        createOpportunity(5, "POLICY123", "AUTO", 100, OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS);
    }

    private void createOpportunity(int id, String priorCarrierGuid, String businessType,
                                 int bookTransferID, OpportunityStatus status) {
        Opportunity opp = OpportunityTestUtils.createOpportunity(id, priorCarrierGuid, businessType, bookTransferID, status);
        opportunityRepository.saveAndFlush(opp);
    }
}
