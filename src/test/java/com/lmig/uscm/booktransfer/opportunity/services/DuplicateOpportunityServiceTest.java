package com.lmig.uscm.booktransfer.opportunity.services;

import com.lmig.uscm.booktransfer.opportunity.client.domain.enums.OpportunityStatus;
import com.lmig.uscm.booktransfer.opportunity.domain.Opportunity;
import com.lmig.uscm.booktransfer.opportunity.repo.OpportunityJDBCRepo;
import com.lmig.uscm.booktransfer.opportunity.services.testutils.OpportunityTestUtils;
import com.lmig.uscm.booktransfer.opportunity.services.testutils.OpportunityTestUtils.TestOpportunitySet;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DuplicateOpportunityServiceTest {

    @Mock
    private OpportunityJDBCRepo opportunityJDBCRepo;

    private DuplicateOpportunityService duplicateOpportunityService;

    @BeforeEach
    void setUp() {
        duplicateOpportunityService = new DuplicateOpportunityService(opportunityJDBCRepo);
    }

    @Test
    void testFindDuplicateOpportunities_WithValidCriteria_ReturnsFilteredDuplicates() {
        // Given
        TestOpportunitySet testSet = OpportunityTestUtils.createStandardTestSet();
        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria("POLICY123", "HOME", 100))
                .thenReturn(testSet.allMatches);

        // When
        List<Opportunity> result = duplicateOpportunityService.findDuplicateOpportunities(testSet.currentOpp);

        // Then
        assertEquals(2, result.size());
        assertFalse(result.contains(testSet.currentOpp));
        assertTrue(result.contains(testSet.duplicate1));
        assertTrue(result.contains(testSet.duplicate2));
    }

    @Test
    void testFindDuplicateOpportunities_WithMissingPriorCarrierGuid_ReturnsEmptyList() {
        // Given
        Opportunity opp = OpportunityTestUtils.createOpportunity(1, null, "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN);

        // When
        List<Opportunity> result = duplicateOpportunityService.findDuplicateOpportunities(opp);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindDuplicateOpportunities_WithMissingBusinessType_ReturnsEmptyList() {
        // Given
        Opportunity opp = OpportunityTestUtils.createOpportunity(1, "POLICY123", null, 100, OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN);

        // When
        List<Opportunity> result = duplicateOpportunityService.findDuplicateOpportunities(opp);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    void testFindLatestActiveOpportunity_WithActiveOpportunities_ReturnsLatest() {
        // Given
        Opportunity opp1 = OpportunityTestUtils.createOpportunity(1, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED);
        Opportunity opp2 = OpportunityTestUtils.createOpportunity(2, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_QUOTED_SUCCESS);
        Opportunity opp3 = OpportunityTestUtils.createOpportunity(3, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN);

        List<Opportunity> opportunities = Arrays.asList(opp1, opp2, opp3);

        // When
        Optional<Opportunity> result = duplicateOpportunityService.findLatestActiveOpportunity(opportunities);

        // Then
        assertTrue(result.isPresent());
        assertEquals(2, result.get().getOpportunityId()); // Latest active (highest ID)
    }

    @Test
    void testFindLatestActiveOpportunity_WithNoActiveOpportunities_ReturnsEmpty() {
        // Given
        Opportunity opp1 = OpportunityTestUtils.createOpportunity(1, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN);
        Opportunity opp2 = OpportunityTestUtils.createOpportunity(2, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_ISSUED);
        Opportunity opp3 = OpportunityTestUtils.createOpportunity(3, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_HERITAGE);
        
        List<Opportunity> opportunities = Arrays.asList(opp1, opp2, opp3);

        // When
        Optional<Opportunity> result = duplicateOpportunityService.findLatestActiveOpportunity(opportunities);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void testFindLatestActiveDuplicateOpportunity_WithActiveDuplicates_ReturnsLatest() {
        // Given
        TestOpportunitySet testSet = OpportunityTestUtils.createStandardTestSet();
        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria("POLICY123", "HOME", 100))
                .thenReturn(testSet.allMatches);

        // When
        Optional<Opportunity> result = duplicateOpportunityService.findLatestActiveDuplicateOpportunity(testSet.currentOpp);

        // Then
        assertTrue(result.isPresent());
        assertEquals(3, result.get().getOpportunityId()); // Latest active duplicate
    }

    @Test
    void testFindLatestActiveDuplicateOpportunity_WithNoActiveDuplicates_ReturnsEmpty() {
        // Given
        TestOpportunitySet testSet = OpportunityTestUtils.createWithdrawnTestSet();
        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria("POLICY123", "HOME", 100))
                .thenReturn(testSet.allMatches);

        // When
        Optional<Opportunity> result = duplicateOpportunityService.findLatestActiveDuplicateOpportunity(testSet.currentOpp);

        // Then
        assertFalse(result.isPresent());
    }

    @Test
    void testIsOpportunityBeingWithdrawn_WithWithdrawnStatus_ReturnsTrue() {
        // Given
        Opportunity opp = OpportunityTestUtils.createOpportunity(1, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN);

        // When
        boolean result = duplicateOpportunityService.isOpportunityBeingWithdrawn(opp);

        // Then
        assertTrue(result);
    }

    @Test
    void testIsOpportunityBeingWithdrawn_WithNonWithdrawnStatus_ReturnsFalse() {
        // Given
        Opportunity opp = OpportunityTestUtils.createOpportunity(1, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED);

        // When
        boolean result = duplicateOpportunityService.isOpportunityBeingWithdrawn(opp);

        // Then
        assertFalse(result);
    }

    @Test
    void testShouldUpdateCustomerPolicy_WithNonWithdrawnOpportunity_ReturnsTrue() {
        // Given
        Opportunity opp = OpportunityTestUtils.createOpportunity(1, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED);

        // When
        boolean result = duplicateOpportunityService.shouldUpdateCustomerPolicy(opp);

        // Then
        assertTrue(result);
    }

    @Test
    void testShouldUpdateCustomerPolicy_WithWithdrawnOpportunityAndNoActiveDuplicates_ReturnsTrue() {
        // Given
        Opportunity currentOpp = OpportunityTestUtils.createOpportunity(1, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN);
        Opportunity duplicate1 = OpportunityTestUtils.createOpportunity(2, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN);

        List<Opportunity> allMatches = Arrays.asList(currentOpp, duplicate1);
        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria("POLICY123", "HOME", 100))
                .thenReturn(allMatches);

        // When
        boolean result = duplicateOpportunityService.shouldUpdateCustomerPolicy(currentOpp);

        // Then
        assertTrue(result);
    }

    @Test
    void testShouldUpdateCustomerPolicy_WithWithdrawnOpportunityAndActiveDuplicates_ReturnsFalse() {
        // Given
        Opportunity currentOpp = OpportunityTestUtils.createOpportunity(1, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN);
        Opportunity duplicate1 = OpportunityTestUtils.createOpportunity(2, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED);

        List<Opportunity> allMatches = Arrays.asList(currentOpp, duplicate1);
        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria("POLICY123", "HOME", 100))
                .thenReturn(allMatches);

        // When
        boolean result = duplicateOpportunityService.shouldUpdateCustomerPolicy(currentOpp);

        // Then
        assertFalse(result);
    }

    @Test
    void testGetOpportunityForCustomerPolicyUpdate_WithNonWithdrawnOpportunity_ReturnsSameOpportunity() {
        // Given
        Opportunity opp = OpportunityTestUtils.createOpportunity(1, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED);

        // When
        Opportunity result = duplicateOpportunityService.getOpportunityForCustomerPolicyUpdate(opp);

        // Then
        assertEquals(opp, result);
    }

    @Test
    void testGetOpportunityForCustomerPolicyUpdate_WithWithdrawnOpportunityAndActiveDuplicate_ReturnsActiveDuplicate() {
        // Given
        Opportunity currentOpp = OpportunityTestUtils.createOpportunity(1, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN);
        Opportunity activeDuplicate = OpportunityTestUtils.createOpportunity(2, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_UNQUOTED);

        List<Opportunity> allMatches = Arrays.asList(currentOpp, activeDuplicate);
        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria("POLICY123", "HOME", 100))
                .thenReturn(allMatches);

        // When
        Opportunity result = duplicateOpportunityService.getOpportunityForCustomerPolicyUpdate(currentOpp);

        // Then
        assertEquals(activeDuplicate, result);
    }

    @Test
    void testGetOpportunityForCustomerPolicyUpdate_WithWithdrawnOpportunityAndNoActiveDuplicate_ReturnsSameOpportunity() {
        // Given
        Opportunity currentOpp = OpportunityTestUtils.createOpportunity(1, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_WITHDRAWN);
        Opportunity inactiveDuplicate = OpportunityTestUtils.createOpportunity(2, "POLICY123", "HOME", 100, OpportunityStatus.OPPORTUNITY_STATUS_ISSUED);
        
        List<Opportunity> allMatches = Arrays.asList(currentOpp, inactiveDuplicate);
        when(opportunityJDBCRepo.findOpportunitiesByCustomerCriteria("POLICY123", "HOME", 100))
                .thenReturn(allMatches);

        // When
        Opportunity result = duplicateOpportunityService.getOpportunityForCustomerPolicyUpdate(currentOpp);

        // Then
        assertEquals(currentOpp, result);
    }


}
